<?php

namespace App\Controllers;

use App\Models\dakoiiUsersModel;

class DakoiiAuth extends BaseController
{
    public $session;
    public $dusersModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dusersModel = new dakoiiUsersModel();
        
        // Ensure session isolation for Dakoii portal
        $this->ensureSessionIsolation();
    }

    /**
     * Ensure session isolation between main app and Dakoii portal
     */
    private function ensureSessionIsolation(): void
    {
        // If user is accessing Dakoii portal but has main app session without Dakoii session,
        // clear main app session to prevent conflicts
        $currentUri = uri_string();

        if (strpos($currentUri, 'dakoii') !== false) {
            // User is accessing Dakoii portal
            if ($this->session->has('logged_in') && !$this->session->has('dakoii_logged_in')) {
                // Has main app session but no Dakoii session - clear main app session
                $mainAppSessionKeys = ['logged_in', 'name', 'role', 'emp_id', 'org_id', 'fileno', 'position', 'status'];
                foreach ($mainAppSessionKeys as $key) {
                    $this->session->remove($key);
                }
                log_message('info', 'Dakoii Portal: Cleared conflicting main app session for session isolation');
            }
        }
    }

    /**
     * Display login page
     */
    public function index()
    {
        return $this->login();
    }

    /**
     * Display login form
     */
    public function login()
    {
        // Redirect if already logged in
        if ($this->session->has('dakoii_logged_in')) {
            return redirect()->to('dakoii/dashboard');
        }

        $data['title'] = "Dakoii Admin Login";
        $data['menu'] = "login";

        return view('dakoii/dakoii_login', $data);
    }

    /**
     * Process login form submission
     */
    public function processLogin()
    {
        // Validate input
        $rules = [
            'username' => 'required|min_length[3]|max_length[255]',
            'password' => 'required|min_length[1]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid username and password');
            return redirect()->to('dakoii/login')->withInput();
        }

        $username = trim($this->request->getPost('username'));
        $password = $this->request->getPost('password');

        // Attempt authentication
        $user = $this->dusersModel->authenticateUser($username, $password);

        if ($user) {
            // Check if user is active
            if (!$user['is_active']) {
                log_message('warning', 'Dakoii Portal: Login attempt with inactive account - Username: ' . $username . ' from IP: ' . $this->request->getIPAddress());
                session()->setFlashdata('error', 'Your account has been deactivated. Please contact the administrator.');
                return redirect()->to('dakoii/login');
            }

            // Set session data for Dakoii portal
            $sessionData = [
                'dakoii_logged_in' => true,
                'dakoii_user_id' => $user['id'],
                'dakoii_name' => $user['name'],
                'dakoii_username' => $user['username'],
                'dakoii_role' => $user['role'],
                'dakoii_orgcode' => $user['orgcode'],
                'dakoii_login_time' => time(),
                'dakoii_login_datetime' => date('Y-m-d H:i:s'),
                'dakoii_ip_address' => $this->request->getIPAddress(),
                'dakoii_user_agent' => $this->request->getUserAgent()->getAgentString(),
                'dakoii_session_id' => session_id(),
                'dakoii_csrf_token' => csrf_hash(),
                'dakoii_last_activity' => time()
            ];

            $this->session->set($sessionData);

            // Log successful login
            log_message('info', 'Dakoii Portal: Successful login - Username: ' . $username . ' (ID: ' . $user['id'] . ') from IP: ' . $this->request->getIPAddress());

            session()->setFlashdata('success', 'Welcome back, ' . $user['name'] . '!');
            return redirect()->to('dakoii/dashboard');

        } else {
            // Log failed login attempt
            log_message('warning', 'Dakoii Portal: Failed login attempt - Username: ' . $username . ' from IP: ' . $this->request->getIPAddress());
            
            session()->setFlashdata('error', 'Invalid username or password. Please try again.');
            return redirect()->to('dakoii/login');
        }
    }

    /**
     * Logout user
     */
    public function logout()
    {
        // Log the logout action
        if ($this->session->has('dakoii_username')) {
            log_message('info', 'Dakoii Portal: User logout: ' . $this->session->get('dakoii_username') . ' from IP: ' . $this->request->getIPAddress());
        }

        // Remove only Dakoii session data (preserve main app session if exists)
        $dakoiiSessionKeys = [
            'dakoii_logged_in', 'dakoii_user_id', 'dakoii_name', 'dakoii_username',
            'dakoii_role', 'dakoii_orgcode', 'dakoii_login_time', 'dakoii_login_datetime',
            'dakoii_ip_address', 'dakoii_user_agent', 'dakoii_session_id', 'dakoii_csrf_token',
            'dakoii_last_activity'
        ];

        foreach ($dakoiiSessionKeys as $key) {
            $this->session->remove($key);
        }

        session()->setFlashdata('success', 'You have been logged out successfully.');
        return redirect()->to('dakoii/login');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }

    /**
     * Middleware to check authentication
     */
    private function requireAuth()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }
        return null;
    }
}
