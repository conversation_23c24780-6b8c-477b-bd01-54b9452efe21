<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Dakoii Users Model
 *
 * This model handles all operations for the Dakoii Admin Portal users.
 * It provides authentication, user management, and security features
 * specifically designed for the separate Dakoii interface portal.
 */
class dakoiiUsersModel extends Model
{
    protected $table      = 'dakoii_users';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;

    // All available fields in the dakoii_users table
    protected $allowedFields = [
        'name',
        'username',
        'password',
        'orgcode',
        'role',
        'is_active'
    ];

    // Enable timestamps
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    // Validation rules for Dakoii users
    protected $validationRules = [
        'name' => [
            'rules' => 'required|min_length[2]|max_length[255]',
            'errors' => [
                'required' => 'Name is required',
                'min_length' => 'Name must be at least 2 characters long',
                'max_length' => 'Name cannot exceed 255 characters'
            ]
        ],
        'username' => [
            'rules' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username,id,{id}]',
            'errors' => [
                'required' => 'Username is required',
                'min_length' => 'Username must be at least 3 characters long',
                'max_length' => 'Username cannot exceed 255 characters',
                'is_unique' => 'Username already exists'
            ]
        ],
        'password' => [
            'rules' => 'required|min_length[6]',
            'errors' => [
                'required' => 'Password is required',
                'min_length' => 'Password must be at least 6 characters long'
            ]
        ],
        'role' => [
            'rules' => 'required|in_list[dakoii,admin,super_admin]',
            'errors' => [
                'required' => 'Role is required',
                'in_list' => 'Invalid role selected'
            ]
        ],
        'is_active' => [
            'rules' => 'in_list[0,1]',
            'errors' => [
                'in_list' => 'Invalid status value'
            ]
        ]
    ];

    protected $validationMessages = [];
    protected $skipValidation     = false;

    // Callbacks
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving to database
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    /**
     * Authenticate user for Dakoii portal
     *
     * @param string $username
     * @param string $password
     * @return array|false Returns user data on success, false on failure
     */
    public function authenticateUser(string $username, string $password)
    {
        // Sanitize username
        $username = trim($username);

        // Find user by username
        $user = $this->where('username', $username)->first();

        if (!$user) {
            log_message('warning', 'Dakoii login attempt with non-existent username: ' . $username);
            return false;
        }

        // Check if account is active
        if (!$user['is_active']) {
            log_message('warning', 'Dakoii login attempt for inactive user: ' . $username);
            return false;
        }

        // Verify password
        if (!password_verify($password, $user['password'])) {
            log_message('warning', 'Dakoii login attempt with incorrect password for user: ' . $username);
            return false;
        }

        // Update last login time (if you want to track this)
        $this->updateLastLogin($user['id']);

        // Remove password from returned data for security
        unset($user['password']);

        log_message('info', 'Successful Dakoii login for user: ' . $username . ' (ID: ' . $user['id'] . ')');

        return $user;
    }

    /**
     * Get active users only
     */
    public function getActiveUsers()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $role)
    {
        return $this->where('role', $role)->findAll();
    }

    /**
     * Get active users by role
     */
    public function getActiveUsersByRole(string $role)
    {
        return $this->where('role', $role)
                   ->where('is_active', 1)
                   ->findAll();
    }

    /**
     * Check if username exists
     */
    public function usernameExists(string $username, int $excludeId = null): bool
    {
        $builder = $this->where('username', $username);

        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }

        return $builder->countAllResults() > 0;
    }

    /**
     * Create new Dakoii user
     */
    public function createUser(array $userData): int|false
    {
        // Validate required fields
        $requiredFields = ['name', 'username', 'password', 'role'];
        foreach ($requiredFields as $field) {
            if (empty($userData[$field])) {
                return false;
            }
        }

        // Set default values
        $userData['is_active'] = $userData['is_active'] ?? 1;
        $userData['orgcode'] = $userData['orgcode'] ?? '';

        // Check if username already exists
        if ($this->usernameExists($userData['username'])) {
            return false;
        }

        return $this->insert($userData);
    }

    /**
     * Update user information
     */
    public function updateUser(int $userId, array $userData): bool
    {
        // Remove password if empty (don't update password)
        if (isset($userData['password']) && empty($userData['password'])) {
            unset($userData['password']);
        }

        // Check username uniqueness if username is being updated
        if (isset($userData['username']) && $this->usernameExists($userData['username'], $userId)) {
            return false;
        }

        return $this->update($userId, $userData);
    }

    /**
     * Activate/Deactivate user
     */
    public function toggleUserStatus(int $userId): bool
    {
        $user = $this->find($userId);
        if (!$user) {
            return false;
        }

        $newStatus = $user['is_active'] ? 0 : 1;
        return $this->update($userId, ['is_active' => $newStatus]);
    }

    /**
     * Get user statistics for dashboard
     */
    public function getUserStats(): array
    {
        return [
            'total_users' => $this->countAllResults(),
            'active_users' => $this->where('is_active', 1)->countAllResults(),
            'inactive_users' => $this->where('is_active', 0)->countAllResults(),
            'admin_users' => $this->where('role', 'admin')->countAllResults(),
            'dakoii_users' => $this->where('role', 'dakoii')->countAllResults(),
            'super_admin_users' => $this->where('role', 'super_admin')->countAllResults()
        ];
    }

    /**
     * Update last login time (optional feature)
     * Note: This would require adding a last_login field to the database
     */
    private function updateLastLogin(int $userId): void
    {
        // This is optional and would require adding last_login field to database
        // $this->update($userId, ['last_login' => date('Y-m-d H:i:s')]);
    }

    /**
     * Get user with safe data (without password)
     */
    public function getSafeUser(int $userId): array|null
    {
        $user = $this->find($userId);
        if ($user) {
            unset($user['password']);
        }
        return $user;
    }

    /**
     * Search users by name or username
     */
    public function searchUsers(string $searchTerm): array
    {
        return $this->groupStart()
                   ->like('name', $searchTerm)
                   ->orLike('username', $searchTerm)
                   ->groupEnd()
                   ->findAll();
    }

    /**
     * Get users with pagination
     */
    public function getUsersPaginated(int $perPage = 10, int $page = 1): array
    {
        $offset = ($page - 1) * $perPage;

        return [
            'users' => $this->orderBy('created_at', 'DESC')
                           ->findAll($perPage, $offset),
            'total' => $this->countAllResults(),
            'per_page' => $perPage,
            'current_page' => $page,
            'total_pages' => ceil($this->countAllResults() / $perPage)
        ];
    }
}